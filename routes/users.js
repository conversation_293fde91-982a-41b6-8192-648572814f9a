const express = require("express")
const { supabase } = require("../config/supabase")
const router = express.Router()

// Get current user profile
router.get("/profile", async (req, res) => {
  try {
    const { data: user, error } = await supabase.from("users").select("*").eq("id", req.user.id).single()

    if (error) {
      console.error("Get user profile error:", error)
      return res.status(500).json({ error: "Failed to fetch user profile" })
    }

    // If provider, include provider profile
    if (user.role === "provider") {
      const { data: providerProfile } = await supabase
        .from("provider_profiles")
        .select("*")
        .eq("user_id", req.user.id)
        .single()

      user.provider_profile = providerProfile
    }

    res.json({ user })
  } catch (error) {
    console.error("Get user profile error:", error)
    res.status(500).json({ error: "Failed to fetch user profile" })
  }
})

// Update user profile
router.put("/profile", async (req, res) => {
  try {
    const { full_name, bio, location, avatar_url } = req.body

    const updateData = {}
    if (full_name !== undefined) updateData.full_name = full_name
    if (bio !== undefined) updateData.bio = bio
    if (location !== undefined) updateData.location = location
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url

    const { data: updatedUser, error } = await supabase
      .from("users")
      .update(updateData)
      .eq("id", req.user.id)
      .select("*")
      .single()

    if (error) {
      console.error("Update user profile error:", error)
      return res.status(500).json({ error: "Failed to update profile" })
    }

    res.json({
      message: "Profile updated successfully",
      user: updatedUser,
    })
  } catch (error) {
    console.error("Update user profile error:", error)
    res.status(500).json({ error: "Failed to update profile" })
  }
})

// Upload avatar
router.post("/avatar", async (req, res) => {
  try {
    const { file_data, file_name } = req.body

    if (!file_data || !file_name) {
      return res.status(400).json({ error: "File data and name are required" })
    }

    // Convert base64 to buffer
    const buffer = Buffer.from(file_data, "base64")
    const fileName = `avatars/${req.user.id}/${Date.now()}-${file_name}`

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage.from("user-uploads").upload(fileName, buffer, {
      contentType: "image/jpeg",
      upsert: true,
    })

    if (error) {
      console.error("Avatar upload error:", error)
      return res.status(500).json({ error: "Failed to upload avatar" })
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from("user-uploads").getPublicUrl(fileName)

    // Update user profile with new avatar URL
    const { error: updateError } = await supabase.from("users").update({ avatar_url: publicUrl }).eq("id", req.user.id)

    if (updateError) {
      console.error("Update avatar URL error:", updateError)
      return res.status(500).json({ error: "Failed to update avatar URL" })
    }

    res.json({
      message: "Avatar uploaded successfully",
      avatar_url: publicUrl,
    })
  } catch (error) {
    console.error("Avatar upload error:", error)
    res.status(500).json({ error: "Failed to upload avatar" })
  }
})

// Get user by ID (public profile)
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params

    const { data: user, error } = await supabase
      .from("users")
      .select("id, full_name, avatar_url, bio, rating, total_reviews, role")
      .eq("id", id)
      .eq("is_active", true)
      .single()

    if (error || !user) {
      return res.status(404).json({ error: "User not found" })
    }

    // If provider, include public provider info
    if (user.role === "provider") {
      const { data: providerProfile } = await supabase
        .from("provider_profiles")
        .select("services, is_approved")
        .eq("user_id", id)
        .single()

      user.provider_profile = providerProfile
    }

    res.json({ user })
  } catch (error) {
    console.error("Get user by ID error:", error)
    res.status(500).json({ error: "Failed to fetch user" })
  }
})

module.exports = router
