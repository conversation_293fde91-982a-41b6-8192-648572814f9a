const express = require("express")
const { supabase } = require("../config/supabase")
const { createNotification } = require("../services/notifications")
const router = express.Router()

// Create a review
router.post("/", async (req, res) => {
  try {
    const { booking_id, reviewee_id, rating, comment, is_anonymous = false } = req.body

    if (!booking_id || !reviewee_id || !rating) {
      return res.status(400).json({
        error: "Missing required fields: booking_id, reviewee_id, rating",
      })
    }

    if (rating < 1 || rating > 5) {
      return res.status(400).json({
        error: "Rating must be between 1 and 5",
      })
    }

    // Verify booking exists and user is part of it
    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .select("*")
      .eq("id", booking_id)
      .eq("status", "completed")
      .single()

    if (bookingError || !booking) {
      return res.status(404).json({ error: "Completed booking not found" })
    }

    // Check if user is part of this booking
    const isCustomer = booking.customer_id === req.user.id
    const isProvider = booking.provider_id === req.user.id

    if (!isCustomer && !isProvider) {
      return res.status(403).json({ error: "You are not part of this booking" })
    }

    // Check if review already exists
    const { data: existingReview } = await supabase
      .from("reviews")
      .select("id")
      .eq("booking_id", booking_id)
      .eq("reviewer_id", req.user.id)
      .single()

    if (existingReview) {
      return res.status(400).json({ error: "You have already reviewed this booking" })
    }

    // Create review
    const { data: review, error: reviewError } = await supabase
      .from("reviews")
      .insert({
        booking_id,
        reviewer_id: req.user.id,
        reviewee_id,
        rating,
        comment,
        is_anonymous,
      })
      .select("*")
      .single()

    if (reviewError) {
      console.error("Create review error:", reviewError)
      return res.status(500).json({ error: "Failed to create review" })
    }

    // Update reviewee's rating
    const { data: allReviews } = await supabase.from("reviews").select("rating").eq("reviewee_id", reviewee_id)

    if (allReviews && allReviews.length > 0) {
      const avgRating = allReviews.reduce((sum, r) => sum + r.rating, 0) / allReviews.length

      await supabase
        .from("users")
        .update({
          rating: Math.round(avgRating * 100) / 100,
          total_reviews: allReviews.length,
        })
        .eq("id", reviewee_id)
    }

    // Send notification to reviewee
    await createNotification({
      user_id: reviewee_id,
      type: "review_received",
      title: "New Review Received",
      message: `You received a ${rating}-star review`,
      data: { booking_id, review_id: review.id },
    })

    res.status(201).json({
      message: "Review created successfully",
      review,
    })
  } catch (error) {
    console.error("Create review error:", error)
    res.status(500).json({ error: "Failed to create review" })
  }
})

// Get reviews for a user
router.get("/user/:user_id", async (req, res) => {
  try {
    const { user_id } = req.params
    const { limit = 20, offset = 0 } = req.query

    const { data: reviews, error } = await supabase
      .from("reviews")
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(id, full_name, avatar_url),
        booking:bookings(id, service:services(title))
      `)
      .eq("reviewee_id", user_id)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Get reviews error:", error)
      return res.status(500).json({ error: "Failed to fetch reviews" })
    }

    // Hide reviewer info for anonymous reviews
    const processedReviews = reviews.map((review) => {
      if (review.is_anonymous) {
        review.reviewer = {
          id: null,
          full_name: "Anonymous",
          avatar_url: null,
        }
      }
      return review
    })

    res.json({ reviews: processedReviews })
  } catch (error) {
    console.error("Get reviews error:", error)
    res.status(500).json({ error: "Failed to fetch reviews" })
  }
})

// Get review for a specific booking
router.get("/booking/:booking_id", async (req, res) => {
  try {
    const { booking_id } = req.params

    // Verify user has access to this booking
    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .select("customer_id, provider_id")
      .eq("id", booking_id)
      .single()

    if (bookingError || !booking) {
      return res.status(404).json({ error: "Booking not found" })
    }

    // Check if user has access to this booking
    const hasAccess =
      req.user.role === "admin" || booking.customer_id === req.user.id || booking.provider_id === req.user.id

    if (!hasAccess) {
      return res.status(403).json({ error: "Access denied" })
    }

    const { data: reviews, error } = await supabase
      .from("reviews")
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(id, full_name, avatar_url),
        reviewee:users!reviews_reviewee_id_fkey(id, full_name, avatar_url)
      `)
      .eq("booking_id", booking_id)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Get booking reviews error:", error)
      return res.status(500).json({ error: "Failed to fetch reviews" })
    }

    res.json({ reviews })
  } catch (error) {
    console.error("Get booking reviews error:", error)
    res.status(500).json({ error: "Failed to fetch reviews" })
  }
})

module.exports = router
