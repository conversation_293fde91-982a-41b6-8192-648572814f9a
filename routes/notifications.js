const express = require("express")
const { supabase } = require("../config/supabase")
const { getUserNotifications, markNotificationAsRead } = require("../services/notifications")
const router = express.Router()

// Get user notifications
router.get("/", async (req, res) => {
  try {
    const { limit = 50, offset = 0, unread_only = false } = req.query

    const notifications = await getUserNotifications(req.user.id, Number.parseInt(limit), Number.parseInt(offset))

    let filteredNotifications = notifications
    if (unread_only === "true") {
      filteredNotifications = notifications.filter((n) => !n.is_read)
    }

    res.json({ notifications: filteredNotifications })
  } catch (error) {
    console.error("Get notifications error:", error)
    res.status(500).json({ error: "Failed to fetch notifications" })
  }
})

// Mark notification as read
router.patch("/:id/read", async (req, res) => {
  try {
    const { id } = req.params

    await markNotificationAsRead(id, req.user.id)

    res.json({ message: "Notification marked as read" })
  } catch (error) {
    console.error("Mark notification read error:", error)
    res.status(500).json({ error: "Failed to mark notification as read" })
  }
})

// Mark all notifications as read
router.patch("/read-all", async (req, res) => {
  try {
    const { error } = await supabase
      .from("notifications")
      .update({ is_read: true })
      .eq("user_id", req.user.id)
      .eq("is_read", false)

    if (error) {
      console.error("Mark all notifications read error:", error)
      return res.status(500).json({ error: "Failed to mark notifications as read" })
    }

    res.json({ message: "All notifications marked as read" })
  } catch (error) {
    console.error("Mark all notifications read error:", error)
    res.status(500).json({ error: "Failed to mark notifications as read" })
  }
})

// Get unread notification count
router.get("/unread-count", async (req, res) => {
  try {
    const { data, error } = await supabase
      .from("notifications")
      .select("id", { count: "exact" })
      .eq("user_id", req.user.id)
      .eq("is_read", false)

    if (error) {
      console.error("Get unread count error:", error)
      return res.status(500).json({ error: "Failed to get unread count" })
    }

    res.json({ unread_count: data?.length || 0 })
  } catch (error) {
    console.error("Get unread count error:", error)
    res.status(500).json({ error: "Failed to get unread count" })
  }
})

module.exports = router
