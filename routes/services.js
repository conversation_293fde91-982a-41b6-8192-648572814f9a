const express = require("express")
const { supabase } = require("../config/supabase")
const { requireRole } = require("../middleware/auth")
const router = express.Router()

// Get all active services
router.get("/", async (req, res) => {
  try {
    const { category, limit = 50, offset = 0 } = req.query

    let query = supabase
      .from("services")
      .select("*")
      .eq("is_active", true)
      .order("title")
      .range(offset, offset + limit - 1)

    if (category) {
      query = query.eq("category", category)
    }

    const { data: services, error } = await query

    if (error) {
      console.error("Get services error:", error)
      return res.status(500).json({ error: "Failed to fetch services" })
    }

    res.json({ services })
  } catch (error) {
    console.error("Get services error:", error)
    res.status(500).json({ error: "Failed to fetch services" })
  }
})

// Get service by ID
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params

    const { data: service, error } = await supabase
      .from("services")
      .select("*")
      .eq("id", id)
      .eq("is_active", true)
      .single()

    if (error || !service) {
      return res.status(404).json({ error: "Service not found" })
    }

    res.json({ service })
  } catch (error) {
    console.error("Get service error:", error)
    res.status(500).json({ error: "Failed to fetch service" })
  }
})

// Create new service (admin only)
router.post("/", requireRole("admin"), async (req, res) => {
  try {
    const { title, description, category, base_price, duration_minutes, add_ons } = req.body

    if (!title || !category || !base_price || !duration_minutes) {
      return res.status(400).json({
        error: "Missing required fields: title, category, base_price, duration_minutes",
      })
    }

    const { data: service, error } = await supabase
      .from("services")
      .insert({
        title,
        description,
        category,
        base_price,
        duration_minutes,
        add_ons: add_ons || [],
      })
      .select("*")
      .single()

    if (error) {
      console.error("Create service error:", error)
      return res.status(500).json({ error: "Failed to create service" })
    }

    res.status(201).json({
      message: "Service created successfully",
      service,
    })
  } catch (error) {
    console.error("Create service error:", error)
    res.status(500).json({ error: "Failed to create service" })
  }
})

// Update service (admin only)
router.put("/:id", requireRole("admin"), async (req, res) => {
  try {
    const { id } = req.params
    const updateData = req.body

    const { data: service, error } = await supabase
      .from("services")
      .update(updateData)
      .eq("id", id)
      .select("*")
      .single()

    if (error) {
      console.error("Update service error:", error)
      return res.status(500).json({ error: "Failed to update service" })
    }

    res.json({
      message: "Service updated successfully",
      service,
    })
  } catch (error) {
    console.error("Update service error:", error)
    res.status(500).json({ error: "Failed to update service" })
  }
})

// Get service categories
router.get("/categories/list", async (req, res) => {
  try {
    const { data: services, error } = await supabase.from("services").select("category").eq("is_active", true)

    if (error) {
      console.error("Get categories error:", error)
      return res.status(500).json({ error: "Failed to fetch categories" })
    }

    const categories = [...new Set(services.map((s) => s.category))]

    res.json({ categories })
  } catch (error) {
    console.error("Get categories error:", error)
    res.status(500).json({ error: "Failed to fetch categories" })
  }
})

module.exports = router
