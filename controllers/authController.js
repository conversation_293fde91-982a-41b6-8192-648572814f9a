const { supabase } = require("../server")

// Send OTP for phone login (customers)
const sendOtp = async (req, res) => {
  try {
    const { phone } = req.body

    if (!phone) {
      return res.status(400).json({ error: "Phone number is required" })
    }

    const { data, error } = await supabase.auth.signInWithOtp({
      phone: phone,
      options: {
        channel: "sms",
      },
    })

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    res.json({
      message: "OTP sent successfully",
      session: data.session,
    })
  } catch (error) {
    console.error("Send OTP error:", error)
    res.status(500).json({ error: "Failed to send OTP" })
  }
}

// Verify OTP and complete phone login
const verifyOtp = async (req, res) => {
  try {
    const { phone, token } = req.body

    if (!phone || !token) {
      return res.status(400).json({ error: "Phone and OTP token are required" })
    }

    const { data, error } = await supabase.auth.verifyOtp({
      phone: phone,
      token: token,
      type: "sms",
    })

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    // Create or update user profile
    const { data: existingUser } = await supabase.from("users").select("*").eq("id", data.user.id).single()

    if (!existingUser) {
      // Create new user profile
      const { error: insertError } = await supabase.from("users").insert({
        id: data.user.id,
        phone: phone,
        full_name: phone, // Temporary, user can update later
        role: "customer",
      })

      if (insertError) {
        console.error("Error creating user profile:", insertError)
      }
    }

    res.json({
      message: "Login successful",
      user: data.user,
      session: data.session,
    })
  } catch (error) {
    console.error("Verify OTP error:", error)
    res.status(500).json({ error: "Failed to verify OTP" })
  }
}

// Email/password signup (providers and admins)
const signup = async (req, res) => {
  try {
    const { email, password, full_name, role = "provider" } = req.body

    if (!email || !password || !full_name) {
      return res.status(400).json({
        error: "Email, password, and full name are required",
      })
    }

    if (!["provider", "admin"].includes(role)) {
      return res.status(400).json({
        error: "Invalid role. Must be provider or admin",
      })
    }

    const { data, error } = await supabase.auth.signUp({
      email: email,
      password: password,
    })

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    // Create user profile
    const { error: insertError } = await supabase.from("users").insert({
      id: data.user.id,
      email: email,
      full_name: full_name,
      role: role,
    })

    if (insertError) {
      console.error("Error creating user profile:", insertError)
      return res.status(500).json({ error: "Failed to create user profile" })
    }

    // If provider, create provider profile
    if (role === "provider") {
      const { error: providerError } = await supabase.from("provider_profiles").insert({
        user_id: data.user.id,
        services: [],
        availability: { is_available: false },
      })

      if (providerError) {
        console.error("Error creating provider profile:", providerError)
      }
    }

    res.json({
      message: "Signup successful. Please check your email for verification.",
      user: data.user,
    })
  } catch (error) {
    console.error("Signup error:", error)
    res.status(500).json({ error: "Failed to create account" })
  }
}

// Email/password login
const login = async (req, res) => {
  try {
    const { email, password } = req.body

    if (!email || !password) {
      return res.status(400).json({ error: "Email and password are required" })
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    })

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    res.json({
      message: "Login successful",
      user: data.user,
      session: data.session,
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Failed to login" })
  }
}

// Logout
const logout = async (req, res) => {
  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    res.json({ message: "Logout successful" })
  } catch (error) {
    console.error("Logout error:", error)
    res.status(500).json({ error: "Failed to logout" })
  }
}

module.exports = {
  sendOtp,
  verifyOtp,
  signup,
  login,
  logout,
}
