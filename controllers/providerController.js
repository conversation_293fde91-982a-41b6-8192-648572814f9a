const { supabase } = require("../server")

// Get job requests for provider
const getProviderRequests = async (req, res) => {
  try {
    const { status = "pending", limit = 20, offset = 0 } = req.query

    const { data: requests, error } = await supabase
      .from("booking_requests")
      .select(`
        *,
        booking:bookings(
          *,
          customer:users!bookings_customer_id_fkey(id, full_name, avatar_url, rating),
          service:services(id, title, category, base_price, description)
        )
      `)
      .eq("provider_id", req.user.id)
      .eq("booking.status", status)
      .order("sent_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Get provider requests error:", error)
      return res.status(500).json({ error: "Failed to fetch job requests" })
    }

    // Filter out expired bookings
    const activeRequests = requests.filter((request) => {
      if (!request.booking) return false
      return new Date() <= new Date(request.booking.expires_at)
    })

    res.json({ requests: activeRequests })
  } catch (error) {
    console.error("Get provider requests error:", error)
    res.status(500).json({ error: "Failed to fetch job requests" })
  }
}

// Update provider profile
const updateProviderProfile = async (req, res) => {
  try {
    const { services, availability, bio, location } = req.body

    // Update user profile
    const userUpdates = {}
    if (bio !== undefined) userUpdates.bio = bio
    if (location !== undefined) userUpdates.location = location

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabase.from("users").update(userUpdates).eq("id", req.user.id)

      if (userError) {
        console.error("Update user profile error:", userError)
        return res.status(500).json({ error: "Failed to update user profile" })
      }
    }

    // Update provider profile
    const providerUpdates = {}
    if (services !== undefined) providerUpdates.services = services
    if (availability !== undefined) providerUpdates.availability = availability

    if (Object.keys(providerUpdates).length > 0) {
      const { data: updatedProfile, error: providerError } = await supabase
        .from("provider_profiles")
        .update(providerUpdates)
        .eq("user_id", req.user.id)
        .select("*")
        .single()

      if (providerError) {
        console.error("Update provider profile error:", providerError)
        return res.status(500).json({ error: "Failed to update provider profile" })
      }

      res.json({
        message: "Profile updated successfully",
        profile: updatedProfile,
      })
    } else {
      res.json({ message: "Profile updated successfully" })
    }
  } catch (error) {
    console.error("Update provider profile error:", error)
    res.status(500).json({ error: "Failed to update profile" })
  }
}

// Get provider profile
const getProviderProfile = async (req, res) => {
  try {
    const { data: profile, error } = await supabase
      .from("provider_profiles")
      .select(`
        *,
        user:users(id, full_name, email, phone, avatar_url, bio, location, rating, total_reviews)
      `)
      .eq("user_id", req.user.id)
      .single()

    if (error) {
      console.error("Get provider profile error:", error)
      return res.status(500).json({ error: "Failed to fetch profile" })
    }

    res.json({ profile })
  } catch (error) {
    console.error("Get provider profile error:", error)
    res.status(500).json({ error: "Failed to fetch profile" })
  }
}

// Get provider earnings/stats
const getProviderStats = async (req, res) => {
  try {
    const { data: bookings, error } = await supabase
      .from("bookings")
      .select("status, service_details, completed_at")
      .eq("provider_id", req.user.id)

    if (error) {
      console.error("Get provider stats error:", error)
      return res.status(500).json({ error: "Failed to fetch stats" })
    }

    const stats = {
      total_jobs: bookings.length,
      completed_jobs: bookings.filter((b) => b.status === "completed").length,
      pending_jobs: bookings.filter((b) => b.status === "pending").length,
      in_progress_jobs: bookings.filter((b) => b.status === "in_progress").length,
      total_earnings: bookings
        .filter((b) => b.status === "completed")
        .reduce((sum, b) => sum + (b.service_details?.total_price || 0), 0),
      this_month_earnings: bookings
        .filter((b) => {
          if (b.status !== "completed" || !b.completed_at) return false
          const completedDate = new Date(b.completed_at)
          const now = new Date()
          return completedDate.getMonth() === now.getMonth() && completedDate.getFullYear() === now.getFullYear()
        })
        .reduce((sum, b) => sum + (b.service_details?.total_price || 0), 0),
    }

    res.json({ stats })
  } catch (error) {
    console.error("Get provider stats error:", error)
    res.status(500).json({ error: "Failed to fetch stats" })
  }
}

// Decline a job request
const declineJobRequest = async (req, res) => {
  try {
    const { booking_id } = req.params

    // Update booking request
    const { error } = await supabase
      .from("booking_requests")
      .update({
        response: "declined",
        responded_at: new Date().toISOString(),
      })
      .eq("booking_id", booking_id)
      .eq("provider_id", req.user.id)

    if (error) {
      console.error("Decline request error:", error)
      return res.status(500).json({ error: "Failed to decline request" })
    }

    res.json({ message: "Request declined successfully" })
  } catch (error) {
    console.error("Decline request error:", error)
    res.status(500).json({ error: "Failed to decline request" })
  }
}

module.exports = {
  getProviderRequests,
  updateProviderProfile,
  getProviderProfile,
  getProviderStats,
  declineJobRequest,
}
