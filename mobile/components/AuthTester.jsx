"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from "react-native"
import OtpLogin from "./OtpLogin"
import EmailPasswordLogin from "./EmailPasswordLogin"

const AuthTester = () => {
  const [authMethod, setAuthMethod] = useState(null) // 'otp' or 'email'
  const [authResult, setAuthResult] = useState(null)
  const [showProfile, setShowProfile] = useState(false)

  const handleLoginSuccess = (data) => {
    setAuthResult(data)
    setShowProfile(true)
  }

  const handleLogout = () => {
    setAuthResult(null)
    setShowProfile(false)
    setAuthMethod(null)
  }

  const renderAuthMethod = () => {
    if (authMethod === "otp") {
      return <OtpLogin onLoginSuccess={handleLoginSuccess} />
    } else if (authMethod === "email") {
      return <EmailPasswordLogin onLoginSuccess={handleLoginSuccess} />
    } else {
      return (
        <View style={styles.methodSelector}>
          <Text style={styles.title}>CleanConnect</Text>
          <Text style={styles.subtitle}>Select Login Method</Text>

          <TouchableOpacity style={[styles.methodButton, styles.customerButton]} onPress={() => setAuthMethod("otp")}>
            <Text style={styles.methodButtonText}>Customer Login (OTP)</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.methodButton, styles.providerButton]} onPress={() => setAuthMethod("email")}>
            <Text style={styles.methodButtonText}>Provider Login (Email)</Text>
          </TouchableOpacity>
        </View>
      )
    }
  }

  const renderProfile = () => {
    if (!authResult) return null

    const { user, session } = authResult

    return (
      <View style={styles.profileContainer}>
        <Text style={styles.title}>Authentication Successful</Text>

        <View style={styles.profileCard}>
          <Text style={styles.profileLabel}>User ID:</Text>
          <Text style={styles.profileValue}>{user?.id || "N/A"}</Text>

          <Text style={styles.profileLabel}>Email:</Text>
          <Text style={styles.profileValue}>{user?.email || "N/A"}</Text>

          <Text style={styles.profileLabel}>Phone:</Text>
          <Text style={styles.profileValue}>{user?.phone || "N/A"}</Text>

          <Text style={styles.profileLabel}>Token:</Text>
          <Text style={styles.profileValue} numberOfLines={1} ellipsizeMode="middle">
            {session?.access_token ? session.access_token.substring(0, 20) + "..." : "N/A"}
          </Text>
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>

        <Text style={styles.noteText}>
          This token can now be used to authenticate API requests by including it in the Authorization header.
        </Text>
      </View>
    )
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {showProfile ? renderProfile() : renderAuthMethod()}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f5f5f5",
  },
  methodSelector: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
    width: "100%",
    maxWidth: 400,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
    color: "#333",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: "center",
    color: "#666",
  },
  methodButton: {
    width: "100%",
    padding: 15,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 15,
  },
  customerButton: {
    backgroundColor: "#4CAF50",
  },
  providerButton: {
    backgroundColor: "#2196F3",
  },
  methodButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  profileContainer: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
    width: "100%",
    maxWidth: 400,
  },
  profileCard: {
    backgroundColor: "#f9f9f9",
    padding: 15,
    borderRadius: 5,
    marginVertical: 15,
  },
  profileLabel: {
    fontWeight: "bold",
    fontSize: 14,
    color: "#666",
    marginTop: 10,
  },
  profileValue: {
    fontSize: 16,
    color: "#333",
    marginBottom: 10,
  },
  logoutButton: {
    backgroundColor: "#f44336",
    padding: 15,
    borderRadius: 5,
    alignItems: "center",
    marginVertical: 15,
  },
  logoutButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  noteText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginTop: 15,
  },
})

export default AuthTester
