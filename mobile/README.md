# CleanConnect Mobile Authentication Components

This directory contains React Native components for authentication in the CleanConnect mobile app. These components are designed to work with the CleanConnect backend API.

## Components

### OtpLogin

A component for customer authentication using phone number and OTP.

**Features:**
- Phone number input validation
- <PERSON><PERSON> request and verification
- Error handling and loading states
- Success callback with user and session data

### EmailPasswordLogin

A component for provider authentication using email and password.

**Features:**
- Login and signup modes
- Form validation
- Error handling and loading states
- Success callback with user and session data

### AuthTester

A component that demonstrates both authentication methods and displays the authentication result.

**Features:**
- Selection between OTP and email/password authentication
- Display of authentication result (user data and token)
- Logout functionality

## Integration with Backend

These components are designed to work with the CleanConnect backend API. They make requests to the following endpoints:

- `POST /api/auth/send-otp`: Send OTP to a phone number
- `POST /api/auth/verify-otp`: Verify OTP and authenticate
- `POST /api/auth/signup`: Create a new provider account
- `POST /api/auth/login`: Authenticate with email and password

## Usage in React Native App

1. **Install dependencies:**

\`\`\`bash
npm install react-native-elements react-native-vector-icons
\`\`\`

2. **Import components:**

\`\`\`jsx
import OtpLogin from './path/to/OtpLogin';
import EmailPasswordLogin from './path/to/EmailPasswordLogin';
\`\`\`

3. **Use in your app:**

\`\`\`jsx
// For customer authentication
<OtpLogin onLoginSuccess={(data) => {
  // Store token and navigate to home screen
  storeToken(data.session.access_token);
  navigation.navigate('Home');
}} />

// For provider authentication
<EmailPasswordLogin onLoginSuccess={(data) => {
  // Store token and navigate to provider dashboard
  storeToken(data.session.access_token);
  navigation.navigate('ProviderDashboard');
}} />
\`\`\`

4. **Store and use the token:**

\`\`\`jsx
// Example using AsyncStorage
import AsyncStorage from '@react-native-async-storage/async-storage';

// Store token
const storeToken = async (token) => {
  try {
    await AsyncStorage.setItem('userToken', token);
  } catch (e) {
    console.error('Failed to save token');
  }
};

// Get token for API requests
const getToken = async () => {
  try {
    return await AsyncStorage.getItem('userToken');
  } catch (e) {
    console.error('Failed to get token');
    return null;
  }
};

// Use token in API requests
const fetchUserProfile = async () => {
  const token = await getToken();
  if (!token) return null;
  
  try {
    const response = await fetch('http://your-api-url/api/users/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return await response.json();
  } catch (error) {
    console.error('API request failed', error);
    return null;
  }
};
\`\`\`

## Testing

To test these components with the CleanConnect backend:

1. Start the backend server:
\`\`\`bash
cd backend
npm install
npm run dev
\`\`\`

2. Update the API_URL in the components to point to your backend server.

3. Run the AuthTest component to test both authentication methods:
\`\`\`jsx
import AuthTest from './path/to/mobile/AuthTest';

// In your app
<AuthTest />
\`\`\`

## Customization

You can customize the styling of these components by modifying the StyleSheet objects in each component file.
\`\`\`

Finally, let's create a package.json file for our project:
