const jwt = require("jsonwebtoken")
const { supabase } = require("../server")

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"]
    const token = authHeader && authHeader.split(" ")[1]

    if (!token) {
      return res.status(401).json({ error: "Access token required" })
    }

    // Verify JWT with Supabase
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token)

    if (error || !user) {
      return res.status(403).json({ error: "Invalid or expired token" })
    }

    // Get user profile from our users table
    const { data: userProfile, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("id", user.id)
      .single()

    if (profileError || !userProfile) {
      return res.status(404).json({ error: "User profile not found" })
    }

    // Attach user info to request
    req.user = {
      id: user.id,
      email: user.email,
      phone: user.phone,
      role: userProfile.role,
      profile: userProfile,
    }

    next()
  } catch (error) {
    console.error("Auth middleware error:", error)
    return res.status(403).json({ error: "Token verification failed" })
  }
}

// Role-based access control middleware
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: "Authentication required" })
    }

    const userRole = req.user.role
    const allowedRoles = Array.isArray(roles) ? roles : [roles]

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        error: "Insufficient permissions",
        required: allowedRoles,
        current: userRole,
      })
    }

    next()
  }
}

module.exports = { authenticateToken, requireRole }
