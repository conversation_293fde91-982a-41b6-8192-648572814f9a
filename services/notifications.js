const { supabase } = require("../config/supabase")

const createNotification = async (notificationData) => {
  try {
    const { error } = await supabase.from("notifications").insert(notificationData)

    if (error) {
      console.error("Create notification error:", error)
      throw error
    }

    // TODO: Integrate with push notification service (FCM, etc.)
    // For now, we just store in database

    console.log("Notification created:", notificationData.title)
  } catch (error) {
    console.error("Notification service error:", error)
    throw error
  }
}

const markNotificationAsRead = async (notificationId, userId) => {
  try {
    const { error } = await supabase
      .from("notifications")
      .update({ is_read: true })
      .eq("id", notificationId)
      .eq("user_id", userId)

    if (error) {
      console.error("Mark notification read error:", error)
      throw error
    }
  } catch (error) {
    console.error("Mark notification read error:", error)
    throw error
  }
}

const getUserNotifications = async (userId, limit = 50, offset = 0) => {
  try {
    const { data: notifications, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Get notifications error:", error)
      throw error
    }

    return notifications
  } catch (error) {
    console.error("Get notifications error:", error)
    throw error
  }
}

module.exports = {
  createNotification,
  markNotificationAsRead,
  getUserNotifications,
}
