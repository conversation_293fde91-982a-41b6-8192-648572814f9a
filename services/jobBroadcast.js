const { supabase } = require("../config/supabase")
const { createNotification } = require("./notifications")

const broadcastJobToProviders = async (booking) => {
  try {
    // Find eligible providers based on:
    // 1. Service type
    // 2. Location (within reasonable distance)
    // 3. Availability
    // 4. Active and approved status

    const { data: eligibleProviders, error } = await supabase
      .from("provider_profiles")
      .select(`
        user_id,
        services,
        availability,
        user:users(id, full_name, location, is_active)
      `)
      .eq("is_approved", true)
      .eq("user.is_active", true)

    if (error) {
      console.error("Error fetching eligible providers:", error)
      return
    }

    // Filter providers who offer this service
    const serviceProviders = eligibleProviders.filter((provider) => {
      return provider.services.includes(booking.service_id) && provider.availability?.is_available === true
    })

    if (serviceProviders.length === 0) {
      console.log("No eligible providers found for booking:", booking.id)
      return
    }

    // TODO: Add location-based filtering
    // For now, we'll broadcast to all eligible providers

    // Create booking requests for each eligible provider
    const bookingRequests = serviceProviders.map((provider) => ({
      booking_id: booking.id,
      provider_id: provider.user_id,
      sent_at: new Date().toISOString(),
    }))

    const { error: requestError } = await supabase.from("booking_requests").insert(bookingRequests)

    if (requestError) {
      console.error("Error creating booking requests:", requestError)
      return
    }

    // Update booking broadcast timestamp
    await supabase.from("bookings").update({ broadcast_at: new Date().toISOString() }).eq("id", booking.id)

    // Send notifications to providers
    const notifications = serviceProviders.map((provider) => ({
      user_id: provider.user_id,
      type: "booking_request",
      title: "New Job Available",
      message: `New ${booking.service?.title || "service"} request in your area`,
      data: { booking_id: booking.id },
    }))

    for (const notification of notifications) {
      await createNotification(notification)
    }

    console.log(`Job broadcast sent to ${serviceProviders.length} providers for booking ${booking.id}`)
  } catch (error) {
    console.error("Job broadcast error:", error)
    throw error
  }
}

// Auto-expire bookings that haven't been accepted
const expireUnacceptedBookings = async () => {
  try {
    const { data: expiredBookings, error } = await supabase
      .from("bookings")
      .select("id, customer_id")
      .eq("status", "pending")
      .lt("expires_at", new Date().toISOString())

    if (error) {
      console.error("Error fetching expired bookings:", error)
      return
    }

    if (expiredBookings.length === 0) {
      return
    }

    // Update expired bookings
    const { error: updateError } = await supabase
      .from("bookings")
      .update({ status: "expired" })
      .in(
        "id",
        expiredBookings.map((b) => b.id),
      )

    if (updateError) {
      console.error("Error updating expired bookings:", updateError)
      return
    }

    // Notify customers about expired bookings
    for (const booking of expiredBookings) {
      await createNotification({
        user_id: booking.customer_id,
        type: "system_alert",
        title: "Booking Expired",
        message: "Your booking request has expired. Please try booking again.",
        data: { booking_id: booking.id },
      })
    }

    console.log(`Expired ${expiredBookings.length} bookings`)
  } catch (error) {
    console.error("Expire bookings error:", error)
  }
}

// Run expiration check every 5 minutes
setInterval(expireUnacceptedBookings, 5 * 60 * 1000)

module.exports = {
  broadcastJobToProviders,
  expireUnacceptedBookings,
}
